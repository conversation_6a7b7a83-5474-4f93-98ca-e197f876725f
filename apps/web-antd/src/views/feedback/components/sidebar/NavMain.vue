<script setup lang="ts">
import type { LucideIcon } from 'lucide-vue-next';

import { onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from '@vben-core/shadcn-ui';

import { ChevronRight } from 'lucide-vue-next';

defineProps<{
  items: {
    icon?: LucideIcon;
    isActive?: boolean;
    items?: {
      title: string;
      url: string;
    }[];
    title: string;
    url: string;
  }[];
}>();

const route = useRoute();
const activeUrl = ref('');

// 根据路径映射到对应的菜单URL
function getMenuUrlFromPath(path: string): string {
  if (path.includes('/feedback/created')) {
    return '/feedback/created';
  } else if (path.includes('/feedback/todo')) {
    return '/feedback/todo';
  } else if (path.includes('/feedback/ticket-detail')) {
    // 工单详情页统一映射到"所有工单"
    return '/feedback';
  } else if (path.includes('/feedback')) {
    return '/feedback';
  }
  return path;
}

// 初始化时根据当前路由设置活动URL
onMounted(() => {
  activeUrl.value = getMenuUrlFromPath(route.path);
});

// 监听路由变化，更新菜单高亮
watch(
  () => route.path,
  (newPath) => {
    activeUrl.value = getMenuUrlFromPath(newPath);
  },
);

// 自定义点击事件处理程序
function handleMenuClick(url: string, event: Event) {
  event.preventDefault(); // 阻止默认的路由导航行为
  activeUrl.value = url;

  // 使用 history.pushState 更新 URL，但不触发页面刷新
  window.history.pushState({}, '', url);

  // 触发一个自定义事件，通知父组件更新内容
  const customEvent = new CustomEvent('menu-click', { detail: { url } });
  window.dispatchEvent(customEvent);
}
</script>

<template>
  <SidebarGroup>
    <SidebarGroupLabel>用户反馈工单</SidebarGroupLabel>
    <SidebarMenu>
      <Collapsible
        v-for="item in items"
        :key="item.title"
        as-child
        :default-open="item.isActive"
        class="group/collapsible"
      >
        <SidebarMenuItem>
          <CollapsibleTrigger as-child>
            <SidebarMenuButton :tooltip="item.title">
              <component :is="item.icon" v-if="item.icon" />
              <span>{{ item.title }}</span>
              <ChevronRight
                class="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"
              />
            </SidebarMenuButton>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <SidebarMenuSub>
              <SidebarMenuSubItem
                v-for="subItem in item.items"
                :key="subItem.title"
              >
                <SidebarMenuSubButton as-child>
                  <a
                    href="#"
                    :class="{
                      'text-primary font-medium': activeUrl === subItem.url,
                    }"
                    @click="(e) => handleMenuClick(subItem.url, e)"
                  >
                    <span>{{ subItem.title }}</span>
                  </a>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
            </SidebarMenuSub>
          </CollapsibleContent>
        </SidebarMenuItem>
      </Collapsible>
    </SidebarMenu>
  </SidebarGroup>
</template>
