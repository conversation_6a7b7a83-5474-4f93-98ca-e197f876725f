<script lang="ts" setup>
import type { FilterOption } from '../../types/ticket';

import { ref, watch } from 'vue';

import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@vben-core/shadcn-ui';

import { useFilterLogic } from '../../composables/useFilterLogic';
import FilterOptionsList from './FilterOptionsList.vue';

// 定义组件属性
const props = defineProps<{
  // 筛选类别键
  categoryKey: string;
  // 筛选类别标签
  categoryLabel: string;
  // 是否打开
  open?: boolean;
  // 筛选选项
  options: FilterOption[];
  // 当前选中的值
  selectedValues: string[];
}>();

// 定义组件事件
const emit = defineEmits<{
  (e: 'update:selectedValues', values: string[]): void;
  (e: 'update:open', open: boolean): void;
  (
    e: 'select',
    categoryKey: string,
    value: string,
    isMultiSelect?: boolean,
  ): void;
}>();

// 下拉菜单是否打开
const isOpen = ref(props.open || false);

// 监听isOpen变化，更新父组件的open属性
watch(isOpen, (newValue) => {
  emit('update:open', newValue);
});

// 监听props.open变化，更新本地isOpen
watch(
  () => props.open,
  (newValue) => {
    if (newValue !== undefined) {
      isOpen.value = newValue;
    }
  },
);

// 初始化筛选逻辑 - 使用ref而不是computed，避免响应式依赖导致的递归更新
const initialFilters = ref<Record<string, string[]>>({
  [props.categoryKey]: [...props.selectedValues],
});

// 当selectedValues变化时更新initialFilters
watch(
  () => props.selectedValues,
  (newValues) => {
    // 始终创建一个新的对象和数组，确保引用变化
    initialFilters.value = { [props.categoryKey]: [...(newValues || [])] };

    // 打印调试信息

    console.warn(
      'QuickFilterMenu watch selectedValues:',
      props.categoryKey,
      JSON.stringify(newValues),
      JSON.stringify(initialFilters.value),
    );
  },
  { deep: true, immediate: true },
);

// 筛选条件变化时的回调函数
function handleFilterChange(filters: Record<string, string[]>) {
  const newValues = filters[props.categoryKey] || [];

  // 只有当值真正变化时才触发事件
  if (JSON.stringify(props.selectedValues) !== JSON.stringify(newValues)) {
    emit('update:selectedValues', newValues);
  }
}

// 使用共享的筛选逻辑
const { toggleFilter: baseToggleFilter } = useFilterLogic(
  initialFilters.value,
  handleFilterChange,
);

// 处理选项切换
function handleToggle(key: string, value: string, isMultiSelect: boolean) {
  // 调用基础的toggleFilter函数
  baseToggleFilter(key, value, undefined, isMultiSelect);

  // 触发select事件，传递类别键、值和是否多选
  emit('select', key, value, isMultiSelect);

  // 如果不是多选模式，则关闭菜单
  if (!isMultiSelect) {
    isOpen.value = false;
  }
}
</script>

<template>
  <DropdownMenu v-model:open="isOpen">
    <DropdownMenuTrigger as-child>
      <slot>
        <Button
          variant="ghost"
          size="sm"
          class="hover:text-primary h-auto cursor-pointer p-0"
        >
          <slot name="trigger-content">
            {{ props.categoryLabel }}
          </slot>
        </Button>
      </slot>
    </DropdownMenuTrigger>

    <DropdownMenuContent class="w-[235px] p-0 shadow-lg" align="start">
      <!-- 使用共享的筛选选项列表组件 -->
      <FilterOptionsList
        :category-key="props.categoryKey"
        :category-label="props.categoryLabel"
        :options="props.options"
        :selected-values="props.selectedValues"
        :show-header="true"
        @toggle="handleToggle"
      />
    </DropdownMenuContent>
  </DropdownMenu>
</template>
