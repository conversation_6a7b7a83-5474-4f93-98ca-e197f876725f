<script lang="ts" setup>
import type { FilterCategory } from '../../types/ticket.ts';

import { computed, ref, watch } from 'vue';

import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
  Input,
} from '@vben-core/shadcn-ui';

import { Filter, Search } from 'lucide-vue-next';

import { useFilterLogic } from '../../composables/useFilterLogic';
import FilterOptionsList from './FilterOptionsList.vue';

// 定义组件属性
const props = withDefaults(
  defineProps<{
    // 筛选类别
    categories?: FilterCategory[];
    // 初始筛选条件
    initialFilters?: Record<string, any>;
  }>(),
  {
    categories: () => [],
    initialFilters: () => ({}),
  },
);

// 定义组件事件
const emit = defineEmits<{
  (e: 'filter', filters: Record<string, string[]>): void;
  (e: 'update:filters', filters: Record<string, string[]>): void;
}>();

// 当前选中的类别
const selectedCategory = ref<null | string>(null);

// 搜索框引用
const searchInputRef = ref<HTMLInputElement>();

// 筛选条件变化时的回调函数
function handleFilterChange(filters: Record<string, string[]>) {
  // 只触发filter事件，不再触发update:filters事件
  emit('filter', filters);
}

// 使用共享的筛选逻辑
const {
  isOpen,
  searchKeyword,
  selectedFilters,
  selectedCount: getSelectedCount,
  toggleFilter,
  updateFilters,
  updateFiltersSilently,
} = useFilterLogic(props.initialFilters, handleFilterChange);

// 初始化选中的筛选项
function initializeFilters() {
  const newFilters: Record<string, string[]> = {};

  // 先确保所有类别都有对应的数组
  for (const category of props.categories || []) {
    newFilters[category.key] = [];
  }

  // 如果有初始筛选条件，应用它们
  if (props.initialFilters) {
    for (const [key, values] of Object.entries(props.initialFilters)) {
      if (Array.isArray(values) && values.length > 0) {
        newFilters[key] = [...values];
      }
    }
  }

  // 更新筛选条件
  updateFilters(newFilters);
}

// 初始化
initializeFilters();

// 监听初始筛选条件的变化
watch(
  () => props.initialFilters,
  (newVal) => {
    // 检查是否真的需要更新，避免无限循环
    const currentFilters = selectedFilters.value;
    const newFiltersStr = JSON.stringify(newVal || {});
    const currentFiltersStr = JSON.stringify(currentFilters);

    // 如果状态已经一致，不需要更新
    if (newFiltersStr === currentFiltersStr) {
      return;
    }

    // 直接更新内部筛选状态，但不触发回调
    const newFilters: Record<string, string[]> = {};

    // 先确保所有类别都有对应的数组
    for (const category of props.categories || []) {
      newFilters[category.key] = [];
    }

    // 如果有新的筛选条件，应用它们
    if (newVal) {
      for (const [key, values] of Object.entries(newVal)) {
        if (Array.isArray(values) && values.length > 0) {
          newFilters[key] = [...values];
        }
      }
    }

    // 使用静默更新，不触发回调避免循环
    updateFiltersSilently(newFilters);
  },
  { deep: true, immediate: true },
);

// 处理选项切换
function handleToggle(key: string, value: string, isMultiSelect: boolean) {
  toggleFilter(key, value, undefined, isMultiSelect);
}

// 监听下拉框关闭事件，清除搜索关键词
watch(isOpen, (newValue) => {
  if (!newValue) {
    // 下拉框关闭时清除搜索关键词
    searchKeyword.value = '';
  }
});

// 筛选后的类别列表
const filteredCategories = computed(() => {
  if (!searchKeyword.value) return props.categories || [];

  // 筛选类别：类别名称匹配或类别下的选项匹配
  return (props.categories || []).filter((category) => {
    // 类别名称匹配
    const categoryMatches = category.label
      .toLowerCase()
      .includes(searchKeyword.value.toLowerCase());

    // 类别下的选项匹配
    const optionsMatch = (category.options || []).some((option) =>
      option.label.toLowerCase().includes(searchKeyword.value.toLowerCase()),
    );

    return categoryMatches || optionsMatch;
  });
});

// 为每个类别筛选选项
function getFilteredOptionsForCategory(category: FilterCategory) {
  if (!searchKeyword.value) return category.options || [];

  const keyword = searchKeyword.value.toLowerCase();

  // 如果类别名称匹配搜索关键词，显示该类别的所有选项
  const categoryMatches = category.label.toLowerCase().includes(keyword);
  if (categoryMatches) {
    return category.options || [];
  }

  // 否则只显示匹配的选项
  return (category.options || []).filter((option) =>
    option.label.toLowerCase().includes(keyword),
  );
}

// 判断类别是否应该默认展开（当有搜索关键词且该类别包含匹配结果时）
function shouldDefaultExpandCategory(_category: FilterCategory): boolean {
  // 为了避免自动展开干扰用户输入，完全禁用自动展开
  // 用户可以通过 hover 或点击来手动展开需要的类别
  return false;
}

// 高亮匹配的文本 - 返回安全的文本片段数组
function getHighlightedTextParts(text: string, keyword: string) {
  if (!keyword || !keyword.trim()) {
    return [{ text, isMatch: false }];
  }

  const parts = [];
  const escapedKeyword = keyword
    .trim()
    .replaceAll(/[.*+?^${}()|[\]\\]/g, String.raw`\$&`);
  const regex = new RegExp(`(${escapedKeyword})`, 'gi');
  let lastIndex = 0;

  const matches = [...text.matchAll(regex)];

  for (const match of matches) {
    if (match.index === undefined) continue;

    // 添加匹配前的文本
    if (match.index > lastIndex) {
      parts.push({
        text: text.slice(lastIndex, match.index),
        isMatch: false,
      });
    }

    // 添加匹配的文本
    parts.push({
      text: match[1],
      isMatch: true,
    });

    lastIndex = match.index + match[0].length;
  }

  // 添加剩余的文本
  if (lastIndex < text.length) {
    parts.push({
      text: text.slice(lastIndex),
      isMatch: false,
    });
  }

  return parts;
}
</script>

<template>
  <DropdownMenu v-model:open="isOpen">
    <DropdownMenuTrigger as-child>
      <Button
        variant="ghost"
        size="sm"
        class="border-border/50 bg-background flex h-8 items-center gap-1.5 px-2"
      >
        <Filter class="size-3.5" />
        筛选
        <span
          v-if="getSelectedCount > 0"
          class="bg-primary/10 text-primary ml-1 rounded-full px-1.5 text-xs"
        >
          {{ getSelectedCount }}
        </span>
      </Button>
    </DropdownMenuTrigger>

    <DropdownMenuContent class="w-[280px] p-0 shadow-lg" align="start">
      <!-- 筛选选项搜索框 -->
      <div class="border-border/20 border-b p-2">
        <div class="relative">
          <Search
            class="text-muted-foreground absolute left-2 top-2 size-3.5"
          />
          <Input
            ref="searchInputRef"
            v-model="searchKeyword"
            class="bg-muted/30 h-8 pl-8 text-sm"
            placeholder="搜索筛选选项..."
            @keydown.stop
          />
        </div>
      </div>

      <!-- 筛选类别列表 -->
      <div class="max-h-[400px] overflow-y-auto">
        <!-- 无搜索结果提示 -->
        <div
          v-if="searchKeyword && filteredCategories.length === 0"
          class="text-muted-foreground flex items-center justify-center py-8 text-sm"
        >
          没有找到匹配的筛选选项
        </div>

        <!-- 类别列表 -->
        <DropdownMenuGroup v-else :key="`categories-${searchKeyword}`">
          <div v-for="category in filteredCategories" :key="category.key">
            <!-- 为每个类别添加子菜单，当有搜索结果时自动展开 -->
            <DropdownMenuSub
              :default-open="shouldDefaultExpandCategory(category)"
            >
              <DropdownMenuSubTrigger
                class="hover:bg-muted/50 flex cursor-pointer items-center justify-between px-3 py-2"
                :class="{ 'bg-muted/30': selectedCategory === category.key }"
              >
                <div class="flex items-center gap-2 text-sm">
                  <component
                    :is="category.icon"
                    v-if="category.icon"
                    class="text-muted-foreground size-3.5"
                  />
                  <span>
                    <template
                      v-for="(part, index) in getHighlightedTextParts(
                        category.label,
                        searchKeyword,
                      )"
                      :key="index"
                    >
                      <span
                        v-if="part.isMatch"
                        class="text-primary font-medium"
                      >
                        {{ part.text }}
                      </span>
                      <span v-else>{{ part.text }}</span>
                    </template>
                  </span>
                </div>
              </DropdownMenuSubTrigger>

              <DropdownMenuSubContent class="w-[280px] p-0">
                <!-- 使用共享的筛选选项列表组件，传入筛选后的选项，保留二级搜索框 -->
                <FilterOptionsList
                  :category-key="category.key"
                  :category-label="category.label"
                  :options="getFilteredOptionsForCategory(category)"
                  :selected-values="selectedFilters[category.key] || []"
                  :show-header="true"
                  :global-search-keyword="searchKeyword"
                  @toggle="handleToggle"
                />
              </DropdownMenuSubContent>
            </DropdownMenuSub>
          </div>
        </DropdownMenuGroup>
      </div>
    </DropdownMenuContent>
  </DropdownMenu>
</template>
